﻿
translate None strings:

    # renpy/common/000statements.rpy:28
    old "Click to play the video."
    new "Click to play the video."

    # renpy/common/00accessibility.rpy:28
    old "Self-voicing disabled."
    new "Self-voicing disabled."

    # renpy/common/00accessibility.rpy:29
    old "Clipboard voicing enabled. "
    new "Clipboard voicing enabled. "

    # renpy/common/00accessibility.rpy:30
    old "Self-voicing enabled. "
    new "Self-voicing enabled. "

    # renpy/common/00accessibility.rpy:32
    old "bar"
    new "bar"

    # renpy/common/00accessibility.rpy:33
    old "selected"
    new "selected"

    # renpy/common/00accessibility.rpy:34
    old "viewport"
    new "viewport"

    # renpy/common/00accessibility.rpy:35
    old "horizontal scroll"
    new "horizontal scroll"

    # renpy/common/00accessibility.rpy:36
    old "vertical scroll"
    new "vertical scroll"

    # renpy/common/00accessibility.rpy:37
    old "activate"
    new "activate"

    # renpy/common/00accessibility.rpy:38
    old "deactivate"
    new "deactivate"

    # renpy/common/00accessibility.rpy:39
    old "increase"
    new "increase"

    # renpy/common/00accessibility.rpy:40
    old "decrease"
    new "decrease"

    # renpy/common/00accessibility.rpy:134
    old "Self-Voicing"
    new "Self-Voicing"

    # renpy/common/00accessibility.rpy:137
    old "Self-voicing support is limited when using a touch screen."
    new "Self-voicing support is limited when using a touch screen."

    # renpy/common/00accessibility.rpy:139
    old "Off"
    new "Off"

    # renpy/common/00accessibility.rpy:143
    old "Text-to-speech"
    new "Text-to-speech"

    # renpy/common/00accessibility.rpy:147
    old "Clipboard"
    new "Clipboard"

    # renpy/common/00accessibility.rpy:151
    old "Debug"
    new "Debug"

    # renpy/common/00accessibility.rpy:155
    old "Voice Volume"
    new "Voice Volume"

    # renpy/common/00accessibility.rpy:163
    old "Reset"
    new "Reset"

    # renpy/common/00accessibility.rpy:167
    old "Self-Voicing Volume Drop"
    new "Self-Voicing Volume Drop"

    # renpy/common/00accessibility.rpy:180
    old "Mono Audio"
    new "Mono Audio"

    # renpy/common/00accessibility.rpy:182
    old "Enable"
    new "Enable"

    # renpy/common/00accessibility.rpy:186
    old "Disable"
    new "Disable"

    # renpy/common/00accessibility.rpy:198
    old "Font Override"
    new "Font Override"

    # renpy/common/00accessibility.rpy:200
    old "Default"
    new "Default"

    # renpy/common/00accessibility.rpy:204
    old "DejaVu Sans"
    new "DejaVu Sans"

    # renpy/common/00accessibility.rpy:208
    old "Opendyslexic"
    new "Opendyslexic"

    # renpy/common/00accessibility.rpy:212
    old "High Contrast Text"
    new "High Contrast Text"

    # renpy/common/00accessibility.rpy:224
    old "Text Size Scaling"
    new "Text Size Scaling"

    # renpy/common/00accessibility.rpy:235
    old "Line Spacing Scaling"
    new "Line Spacing Scaling"

    # renpy/common/00accessibility.rpy:246
    old "Kerning"
    new "Kerning"

    # renpy/common/00accessibility.rpy:267
    old "Accessibility Menu. Use up and down arrows to navigate, and enter to activate buttons and bars."
    new "Accessibility Menu. Use up and down arrows to navigate, and enter to activate buttons and bars."

    # renpy/common/00accessibility.rpy:288
    old "Self-Voicing and Audio"
    new "Self-Voicing and Audio"

    # renpy/common/00accessibility.rpy:292
    old "Text"
    new "Text"

    # renpy/common/00accessibility.rpy:296
    old "Return"
    new "Return"

    # renpy/common/00accessibility.rpy:306
    old "The options on this menu are intended to improve accessibility. They may not work with all games, and some combinations of options may render the game unplayable. This is not an issue with the game or engine. For the best results when changing fonts, try to keep the text size the same as it originally was."
    new "The options on this menu are intended to improve accessibility. They may not work with all games, and some combinations of options may render the game unplayable. This is not an issue with the game or engine. For the best results when changing fonts, try to keep the text size the same as it originally was."

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Monday"
    new "{#weekday}Monday"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Tuesday"
    new "{#weekday}Tuesday"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Wednesday"
    new "{#weekday}Wednesday"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Thursday"
    new "{#weekday}Thursday"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Friday"
    new "{#weekday}Friday"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Saturday"
    new "{#weekday}Saturday"

    # renpy/common/00action_file.rpy:26
    old "{#weekday}Sunday"
    new "{#weekday}Sunday"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Mon"
    new "{#weekday_short}Mon"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Tue"
    new "{#weekday_short}Tue"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Wed"
    new "{#weekday_short}Wed"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Thu"
    new "{#weekday_short}Thu"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Fri"
    new "{#weekday_short}Fri"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Sat"
    new "{#weekday_short}Sat"

    # renpy/common/00action_file.rpy:37
    old "{#weekday_short}Sun"
    new "{#weekday_short}Sun"

    # renpy/common/00action_file.rpy:47
    old "{#month}January"
    new "{#month}January"

    # renpy/common/00action_file.rpy:47
    old "{#month}February"
    new "{#month}February"

    # renpy/common/00action_file.rpy:47
    old "{#month}March"
    new "{#month}March"

    # renpy/common/00action_file.rpy:47
    old "{#month}April"
    new "{#month}April"

    # renpy/common/00action_file.rpy:47
    old "{#month}May"
    new "{#month}May"

    # renpy/common/00action_file.rpy:47
    old "{#month}June"
    new "{#month}June"

    # renpy/common/00action_file.rpy:47
    old "{#month}July"
    new "{#month}July"

    # renpy/common/00action_file.rpy:47
    old "{#month}August"
    new "{#month}August"

    # renpy/common/00action_file.rpy:47
    old "{#month}September"
    new "{#month}September"

    # renpy/common/00action_file.rpy:47
    old "{#month}October"
    new "{#month}October"

    # renpy/common/00action_file.rpy:47
    old "{#month}November"
    new "{#month}November"

    # renpy/common/00action_file.rpy:47
    old "{#month}December"
    new "{#month}December"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Jan"
    new "{#month_short}Jan"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Feb"
    new "{#month_short}Feb"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Mar"
    new "{#month_short}Mar"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Apr"
    new "{#month_short}Apr"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}May"
    new "{#month_short}May"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Jun"
    new "{#month_short}Jun"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Jul"
    new "{#month_short}Jul"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Aug"
    new "{#month_short}Aug"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Sep"
    new "{#month_short}Sep"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Oct"
    new "{#month_short}Oct"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Nov"
    new "{#month_short}Nov"

    # renpy/common/00action_file.rpy:63
    old "{#month_short}Dec"
    new "{#month_short}Dec"

    # renpy/common/00action_file.rpy:258
    old "%b %d, %H:%M"
    new "%b %d, %H:%M"

    # renpy/common/00action_file.rpy:395
    old "Save slot %s: [text]"
    new "Save slot %s: [text]"

    # renpy/common/00action_file.rpy:481
    old "Load slot %s: [text]"
    new "Load slot %s: [text]"

    # renpy/common/00action_file.rpy:534
    old "Delete slot [text]"
    new "Delete slot [text]"

    # renpy/common/00action_file.rpy:613
    old "File page auto"
    new "File page auto"

    # renpy/common/00action_file.rpy:615
    old "File page quick"
    new "File page quick"

    # renpy/common/00action_file.rpy:617
    old "File page [text]"
    new "File page [text]"

    # renpy/common/00action_file.rpy:675
    old "Page {}"
    new "Page {}"

    # renpy/common/00action_file.rpy:675
    old "Automatic saves"
    new "Automatic saves"

    # renpy/common/00action_file.rpy:675
    old "Quick saves"
    new "Quick saves"

    # renpy/common/00action_file.rpy:816
    old "Next file page."
    new "Next file page."

    # renpy/common/00action_file.rpy:888
    old "Previous file page."
    new "Previous file page."

    # renpy/common/00action_file.rpy:949
    old "Quick save complete."
    new "Quick save complete."

    # renpy/common/00action_file.rpy:964
    old "Quick save."
    new "Quick save."

    # renpy/common/00action_file.rpy:983
    old "Quick load."
    new "Quick load."

    # renpy/common/00action_other.rpy:416
    old "Language [text]"
    new "Language [text]"

    # renpy/common/00action_other.rpy:786
    old "Open [text] directory."
    new "Open [text] directory."

    # renpy/common/00director.rpy:712
    old "The interactive director is not enabled here."
    new "The interactive director is not enabled here."

    # renpy/common/00director.rpy:1512
    old "⬆"
    new "⬆"

    # renpy/common/00director.rpy:1518
    old "⬇"
    new "⬇"

    # renpy/common/00director.rpy:1582
    old "Done"
    new "Done"

    # renpy/common/00director.rpy:1592
    old "(statement)"
    new "(statement)"

    # renpy/common/00director.rpy:1593
    old "(tag)"
    new "(tag)"

    # renpy/common/00director.rpy:1594
    old "(attributes)"
    new "(attributes)"

    # renpy/common/00director.rpy:1595
    old "(transform)"
    new "(transform)"

    # renpy/common/00director.rpy:1620
    old "(transition)"
    new "(transition)"

    # renpy/common/00director.rpy:1632
    old "(channel)"
    new "(channel)"

    # renpy/common/00director.rpy:1633
    old "(filename)"
    new "(filename)"

    # renpy/common/00director.rpy:1662
    old "Change"
    new "Change"

    # renpy/common/00director.rpy:1664
    old "Add"
    new "Add"

    # renpy/common/00director.rpy:1667
    old "Cancel"
    new "Cancel"

    # renpy/common/00director.rpy:1670
    old "Remove"
    new "Remove"

    # renpy/common/00director.rpy:1705
    old "Statement:"
    new "Statement:"

    # renpy/common/00director.rpy:1726
    old "Tag:"
    new "Tag:"

    # renpy/common/00director.rpy:1742
    old "Attributes:"
    new "Attributes:"

    # renpy/common/00director.rpy:1753
    old "Click to toggle attribute, right click to toggle negative attribute."
    new "Click to toggle attribute, right click to toggle negative attribute."

    # renpy/common/00director.rpy:1765
    old "Transforms:"
    new "Transforms:"

    # renpy/common/00director.rpy:1776
    old "Click to set transform, right click to add to transform list."
    new "Click to set transform, right click to add to transform list."

    # renpy/common/00director.rpy:1777
    old "Customize director.transforms to add more transforms."
    new "Customize director.transforms to add more transforms."

    # renpy/common/00director.rpy:1789
    old "Behind:"
    new "Behind:"

    # renpy/common/00director.rpy:1800
    old "Click to set, right click to add to behind list."
    new "Click to set, right click to add to behind list."

    # renpy/common/00director.rpy:1812
    old "Transition:"
    new "Transition:"

    # renpy/common/00director.rpy:1822
    old "Click to set."
    new "Click to set."

    # renpy/common/00director.rpy:1823
    old "Customize director.transitions to add more transitions."
    new "Customize director.transitions to add more transitions."

    # renpy/common/00director.rpy:1835
    old "Channel:"
    new "Channel:"

    # renpy/common/00director.rpy:1846
    old "Customize director.audio_channels to add more channels."
    new "Customize director.audio_channels to add more channels."

    # renpy/common/00director.rpy:1858
    old "Audio Filename:"
    new "Audio Filename:"

    # renpy/common/00gui.rpy:448
    old "Are you sure?"
    new "Are you sure?"

    # renpy/common/00gui.rpy:449
    old "Are you sure you want to delete this save?"
    new "Are you sure you want to delete this save?"

    # renpy/common/00gui.rpy:450
    old "Are you sure you want to overwrite your save?"
    new "Are you sure you want to overwrite your save?"

    # renpy/common/00gui.rpy:451
    old "Loading will lose unsaved progress.\nAre you sure you want to do this?"
    new "Loading will lose unsaved progress.\nAre you sure you want to do this?"

    # renpy/common/00gui.rpy:452
    old "Are you sure you want to quit?"
    new "Are you sure you want to quit?"

    # renpy/common/00gui.rpy:453
    old "Are you sure you want to return to the main menu?\nThis will lose unsaved progress."
    new "Are you sure you want to return to the main menu?\nThis will lose unsaved progress."

    # renpy/common/00gui.rpy:454
    old "Are you sure you want to continue where you left off?"
    new "Are you sure you want to continue where you left off?"

    # renpy/common/00gui.rpy:455
    old "Are you sure you want to end the replay?"
    new "Are you sure you want to end the replay?"

    # renpy/common/00gui.rpy:456
    old "Are you sure you want to begin skipping?"
    new "Are you sure you want to begin skipping?"

    # renpy/common/00gui.rpy:457
    old "Are you sure you want to skip to the next choice?"
    new "Are you sure you want to skip to the next choice?"

    # renpy/common/00gui.rpy:458
    old "Are you sure you want to skip unseen dialogue to the next choice?"
    new "Are you sure you want to skip unseen dialogue to the next choice?"

    # renpy/common/00gui.rpy:459
    old "This save was created on a different device. Maliciously constructed save files can harm your computer. Do you trust this save's creator and everyone who could have changed the file?"
    new "This save was created on a different device. Maliciously constructed save files can harm your computer. Do you trust this save's creator and everyone who could have changed the file?"

    # renpy/common/00gui.rpy:460
    old "Do you trust the device the save was created on? You should only choose yes if you are the device's sole user."
    new "Do you trust the device the save was created on? You should only choose yes if you are the device's sole user."

    # renpy/common/00keymap.rpy:325
    old "Failed to save screenshot as %s."
    new "Failed to save screenshot as %s."

    # renpy/common/00keymap.rpy:346
    old "Saved screenshot as %s."
    new "Saved screenshot as %s."

    # renpy/common/00library.rpy:257
    old "Skip Mode"
    new "Skip Mode"

    # renpy/common/00library.rpy:344
    old "This program contains free software under a number of licenses, including the MIT License and GNU Lesser General Public License. A complete list of software, including links to full source code, can be found {a=https://www.renpy.org/l/license}here{/a}."
    new "This program contains free software under a number of licenses, including the MIT License and GNU Lesser General Public License. A complete list of software, including links to full source code, can be found {a=https://www.renpy.org/l/license}here{/a}."

    # renpy/common/00preferences.rpy:295
    old "display"
    new "display"

    # renpy/common/00preferences.rpy:315
    old "transitions"
    new "transitions"

    # renpy/common/00preferences.rpy:324
    old "skip transitions"
    new "skip transitions"

    # renpy/common/00preferences.rpy:326
    old "video sprites"
    new "video sprites"

    # renpy/common/00preferences.rpy:335
    old "show empty window"
    new "show empty window"

    # renpy/common/00preferences.rpy:344
    old "text speed"
    new "text speed"

    # renpy/common/00preferences.rpy:352
    old "joystick"
    new "joystick"

    # renpy/common/00preferences.rpy:352
    old "joystick..."
    new "joystick..."

    # renpy/common/00preferences.rpy:359
    old "skip"
    new "skip"

    # renpy/common/00preferences.rpy:362
    old "skip unseen [text]"
    new "skip unseen [text]"

    # renpy/common/00preferences.rpy:367
    old "skip unseen text"
    new "skip unseen text"

    # renpy/common/00preferences.rpy:369
    old "begin skipping"
    new "begin skipping"

    # renpy/common/00preferences.rpy:373
    old "after choices"
    new "after choices"

    # renpy/common/00preferences.rpy:380
    old "skip after choices"
    new "skip after choices"

    # renpy/common/00preferences.rpy:382
    old "auto-forward time"
    new "auto-forward time"

    # renpy/common/00preferences.rpy:396
    old "auto-forward"
    new "auto-forward"

    # renpy/common/00preferences.rpy:403
    old "Auto forward"
    new "Auto forward"

    # renpy/common/00preferences.rpy:406
    old "auto-forward after click"
    new "auto-forward after click"

    # renpy/common/00preferences.rpy:415
    old "automatic move"
    new "automatic move"

    # renpy/common/00preferences.rpy:424
    old "wait for voice"
    new "wait for voice"

    # renpy/common/00preferences.rpy:433
    old "voice sustain"
    new "voice sustain"

    # renpy/common/00preferences.rpy:442
    old "self voicing"
    new "self voicing"

    # renpy/common/00preferences.rpy:445
    old "self voicing enable"
    new "self voicing enable"

    # renpy/common/00preferences.rpy:447
    old "self voicing disable"
    new "self voicing disable"

    # renpy/common/00preferences.rpy:451
    old "self voicing volume drop"
    new "self voicing volume drop"

    # renpy/common/00preferences.rpy:459
    old "clipboard voicing"
    new "clipboard voicing"

    # renpy/common/00preferences.rpy:462
    old "clipboard voicing enable"
    new "clipboard voicing enable"

    # renpy/common/00preferences.rpy:464
    old "clipboard voicing disable"
    new "clipboard voicing disable"

    # renpy/common/00preferences.rpy:468
    old "debug voicing"
    new "debug voicing"

    # renpy/common/00preferences.rpy:471
    old "debug voicing enable"
    new "debug voicing enable"

    # renpy/common/00preferences.rpy:473
    old "debug voicing disable"
    new "debug voicing disable"

    # renpy/common/00preferences.rpy:477
    old "emphasize audio"
    new "emphasize audio"

    # renpy/common/00preferences.rpy:486
    old "rollback side"
    new "rollback side"

    # renpy/common/00preferences.rpy:496
    old "gl powersave"
    new "gl powersave"

    # renpy/common/00preferences.rpy:502
    old "gl framerate"
    new "gl framerate"

    # renpy/common/00preferences.rpy:505
    old "gl tearing"
    new "gl tearing"

    # renpy/common/00preferences.rpy:508
    old "font transform"
    new "font transform"

    # renpy/common/00preferences.rpy:511
    old "font size"
    new "font size"

    # renpy/common/00preferences.rpy:519
    old "font line spacing"
    new "font line spacing"

    # renpy/common/00preferences.rpy:527
    old "system cursor"
    new "system cursor"

    # renpy/common/00preferences.rpy:536
    old "renderer menu"
    new "renderer menu"

    # renpy/common/00preferences.rpy:539
    old "accessibility menu"
    new "accessibility menu"

    # renpy/common/00preferences.rpy:542
    old "high contrast text"
    new "high contrast text"

    # renpy/common/00preferences.rpy:551
    old "audio when minimized"
    new "audio when minimized"

    # renpy/common/00preferences.rpy:560
    old "audio when unfocused"
    new "audio when unfocused"

    # renpy/common/00preferences.rpy:569
    old "web cache preload"
    new "web cache preload"

    # renpy/common/00preferences.rpy:584
    old "voice after game menu"
    new "voice after game menu"

    # renpy/common/00preferences.rpy:593
    old "restore window position"
    new "restore window position"

    # renpy/common/00preferences.rpy:602
    old "mono audio"
    new "mono audio"

    # renpy/common/00preferences.rpy:611
    old "font kerning"
    new "font kerning"

    # renpy/common/00preferences.rpy:619
    old "reset"
    new "reset"

    # renpy/common/00preferences.rpy:632
    old "main volume"
    new "main volume"

    # renpy/common/00preferences.rpy:633
    old "music volume"
    new "music volume"

    # renpy/common/00preferences.rpy:634
    old "sound volume"
    new "sound volume"

    # renpy/common/00preferences.rpy:635
    old "voice volume"
    new "voice volume"

    # renpy/common/00preferences.rpy:636
    old "mute main"
    new "mute main"

    # renpy/common/00preferences.rpy:637
    old "mute music"
    new "mute music"

    # renpy/common/00preferences.rpy:638
    old "mute sound"
    new "mute sound"

    # renpy/common/00preferences.rpy:639
    old "mute voice"
    new "mute voice"

    # renpy/common/00preferences.rpy:640
    old "mute all"
    new "mute all"

    # renpy/common/00preferences.rpy:723
    old "Clipboard voicing enabled. Press 'shift+C' to disable."
    new "Clipboard voicing enabled. Press 'shift+C' to disable."

    # renpy/common/00preferences.rpy:725
    old "Self-voicing would say \"[renpy.display.tts.last]\". Press 'alt+shift+V' to disable."
    new "Self-voicing would say \"[renpy.display.tts.last]\". Press 'alt+shift+V' to disable."

    # renpy/common/00preferences.rpy:727
    old "Self-voicing enabled. Press 'v' to disable."
    new "Self-voicing enabled. Press 'v' to disable."

    # renpy/common/00speechbubble.rpy:420
    old "Speech Bubble Editor"
    new "Speech Bubble Editor"

    # renpy/common/00speechbubble.rpy:425
    old "(hide)"
    new "(hide)"

    # renpy/common/00speechbubble.rpy:436
    old "(clear retained bubbles)"
    new "(clear retained bubbles)"

    # renpy/common/00sync.rpy:70
    old "Sync downloaded."
    new "Sync downloaded."

    # renpy/common/00sync.rpy:184
    old "Could not connect to the Ren'Py Sync server."
    new "Could not connect to the Ren'Py Sync server."

    # renpy/common/00sync.rpy:186
    old "The Ren'Py Sync server timed out."
    new "The Ren'Py Sync server timed out."

    # renpy/common/00sync.rpy:188
    old "An unknown error occurred while connecting to the Ren'Py Sync server."
    new "An unknown error occurred while connecting to the Ren'Py Sync server."

    # renpy/common/00sync.rpy:204
    old "The Ren'Py Sync server does not have a copy of this sync. The sync ID may be invalid, or it may have timed out."
    new "The Ren'Py Sync server does not have a copy of this sync. The sync ID may be invalid, or it may have timed out."

    # renpy/common/00sync.rpy:305
    old "Please enter the sync ID you generated.\nNever enter a sync ID you didn't create yourself."
    new "Please enter the sync ID you generated.\nNever enter a sync ID you didn't create yourself."

    # renpy/common/00sync.rpy:324
    old "The sync ID is not in the correct format."
    new "The sync ID is not in the correct format."

    # renpy/common/00sync.rpy:344
    old "The sync could not be decrypted."
    new "The sync could not be decrypted."

    # renpy/common/00sync.rpy:367
    old "The sync belongs to a different game."
    new "The sync belongs to a different game."

    # renpy/common/00sync.rpy:372
    old "The sync contains a file with an invalid name."
    new "The sync contains a file with an invalid name."

    # renpy/common/00sync.rpy:425
    old "This will upload your saves to the {a=https://sync.renpy.org}Ren'Py Sync Server{/a}.\nDo you want to continue?"
    new "This will upload your saves to the {a=https://sync.renpy.org}Ren'Py Sync Server{/a}.\nDo you want to continue?"

    # renpy/common/00sync.rpy:433
    old "Yes"
    new "Yes"

    # renpy/common/00sync.rpy:434
    old "No"
    new "No"

    # renpy/common/00sync.rpy:457
    old "Enter Sync ID"
    new "Enter Sync ID"

    # renpy/common/00sync.rpy:468
    old "This will contact the {a=https://sync.renpy.org}Ren'Py Sync Server{/a}."
    new "This will contact the {a=https://sync.renpy.org}Ren'Py Sync Server{/a}."

    # renpy/common/00sync.rpy:498
    old "Sync Success"
    new "Sync Success"

    # renpy/common/00sync.rpy:501
    old "The Sync ID is:"
    new "The Sync ID is:"

    # renpy/common/00sync.rpy:507
    old "You can use this ID to download your save on another device.\nThis sync will expire in an hour.\nRen'Py Sync is supported by {a=https://www.renpy.org/sponsors.html}Ren'Py's Sponsors{/a}."
    new "You can use this ID to download your save on another device.\nThis sync will expire in an hour.\nRen'Py Sync is supported by {a=https://www.renpy.org/sponsors.html}Ren'Py's Sponsors{/a}."

    # renpy/common/00sync.rpy:511
    old "Continue"
    new "Continue"

    # renpy/common/00sync.rpy:536
    old "Sync Error"
    new "Sync Error"

    # renpy/common/00translation.rpy:63
    old "Translation identifier: [identifier]"
    new "Translation identifier: [identifier]"

    # renpy/common/00translation.rpy:84
    old " translates [tl.filename]:[tl.linenumber]"
    new " translates [tl.filename]:[tl.linenumber]"

    # renpy/common/00translation.rpy:101
    old "\n{color=#fff}Copied to clipboard.{/color}"
    new "\n{color=#fff}Copied to clipboard.{/color}"

    # renpy/common/00iap.rpy:231
    old "Contacting App Store\nPlease Wait..."
    new "Contacting App Store\nPlease Wait..."

    # renpy/common/00updater.rpy:415
    old "No update methods found."
    new "No update methods found."

    # renpy/common/00updater.rpy:462
    old "Could not download file list: "
    new "Could not download file list: "

    # renpy/common/00updater.rpy:465
    old "File list digest does not match."
    new "File list digest does not match."

    # renpy/common/00updater.rpy:675
    old "An error is being simulated."
    new "An error is being simulated."

    # renpy/common/00updater.rpy:863
    old "Either this project does not support updating, or the update status file was deleted."
    new "Either this project does not support updating, or the update status file was deleted."

    # renpy/common/00updater.rpy:877
    old "This account does not have permission to perform an update."
    new "This account does not have permission to perform an update."

    # renpy/common/00updater.rpy:880
    old "This account does not have permission to write the update log."
    new "This account does not have permission to write the update log."

    # renpy/common/00updater.rpy:966
    old "Could not verify update signature."
    new "Could not verify update signature."

    # renpy/common/00updater.rpy:1289
    old "The update file was not downloaded."
    new "The update file was not downloaded."

    # renpy/common/00updater.rpy:1307
    old "The update file does not have the correct digest - it may have been corrupted."
    new "The update file does not have the correct digest - it may have been corrupted."

    # renpy/common/00updater.rpy:1457
    old "While unpacking {}, unknown type {}."
    new "While unpacking {}, unknown type {}."

    # renpy/common/00updater.rpy:1928
    old "Updater"
    new "Updater"

    # renpy/common/00updater.rpy:1935
    old "An error has occurred:"
    new "An error has occurred:"

    # renpy/common/00updater.rpy:1937
    old "Checking for updates."
    new "Checking for updates."

    # renpy/common/00updater.rpy:1939
    old "This program is up to date."
    new "This program is up to date."

    # renpy/common/00updater.rpy:1941
    old "[u.version] is available. Do you want to install it?"
    new "[u.version] is available. Do you want to install it?"

    # renpy/common/00updater.rpy:1943
    old "Preparing to download the updates."
    new "Preparing to download the updates."

    # renpy/common/00updater.rpy:1945
    old "Downloading the updates."
    new "Downloading the updates."

    # renpy/common/00updater.rpy:1947
    old "Unpacking the updates."
    new "Unpacking the updates."

    # renpy/common/00updater.rpy:1949
    old "Finishing up."
    new "Finishing up."

    # renpy/common/00updater.rpy:1951
    old "The updates have been installed. The program will restart."
    new "The updates have been installed. The program will restart."

    # renpy/common/00updater.rpy:1953
    old "The updates have been installed."
    new "The updates have been installed."

    # renpy/common/00updater.rpy:1955
    old "The updates were cancelled."
    new "The updates were cancelled."

    # renpy/common/00updater.rpy:1970
    old "Proceed"
    new "Proceed"

    # renpy/common/00updater.rpy:1986
    old "Preparing to download the game data."
    new "Preparing to download the game data."

    # renpy/common/00updater.rpy:1988
    old "Downloading the game data."
    new "Downloading the game data."

    # renpy/common/00updater.rpy:1990
    old "The game data has been downloaded."
    new "The game data has been downloaded."

    # renpy/common/00updater.rpy:1992
    old "An error occurred when trying to download game data:"
    new "An error occurred when trying to download game data:"

    # renpy/common/00updater.rpy:1997
    old "This game cannot be run until the game data has been downloaded."
    new "This game cannot be run until the game data has been downloaded."

    # renpy/common/00updater.rpy:2004
    old "Retry"
    new "Retry"

    # renpy/common/00compat.rpy:456
    old "Fullscreen"
    new "Fullscreen"

    # renpy/common/00gallery.rpy:676
    old "Image [index] of [count] locked."
    new "Image [index] of [count] locked."

    # renpy/common/00gallery.rpy:696
    old "prev"
    new "prev"

    # renpy/common/00gallery.rpy:697
    old "next"
    new "next"

    # renpy/common/00gallery.rpy:698
    old "slideshow"
    new "slideshow"

    # renpy/common/00gallery.rpy:699
    old "return"
    new "return"

    # renpy/common/00gltest.rpy:89
    old "Renderer"
    new "Renderer"

    # renpy/common/00gltest.rpy:91
    old "Automatically Choose"
    new "Automatically Choose"

    # renpy/common/00gltest.rpy:96
    old "Force GL2 Renderer"
    new "Force GL2 Renderer"

    # renpy/common/00gltest.rpy:101
    old "Force ANGLE2 Renderer"
    new "Force ANGLE2 Renderer"

    # renpy/common/00gltest.rpy:106
    old "Force GLES2 Renderer"
    new "Force GLES2 Renderer"

    # renpy/common/00gltest.rpy:110
    old "Gamepad"
    new "Gamepad"

    # renpy/common/00gltest.rpy:112
    old "Enable (No Blocklist)"
    new "Enable (No Blocklist)"

    # renpy/common/00gltest.rpy:126
    old "Calibrate"
    new "Calibrate"

    # renpy/common/00gltest.rpy:135
    old "Powersave"
    new "Powersave"

    # renpy/common/00gltest.rpy:145
    old "Framerate"
    new "Framerate"

    # renpy/common/00gltest.rpy:147
    old "Screen"
    new "Screen"

    # renpy/common/00gltest.rpy:151
    old "60"
    new "60"

    # renpy/common/00gltest.rpy:155
    old "30"
    new "30"

    # renpy/common/00gltest.rpy:159
    old "Tearing"
    new "Tearing"

    # renpy/common/00gltest.rpy:171
    old "Changes will take effect the next time this program is run."
    new "Changes will take effect the next time this program is run."

    # renpy/common/00gltest.rpy:178
    old "Quit"
    new "Quit"

    # renpy/common/00gltest.rpy:207
    old "Performance Warning"
    new "Performance Warning"

    # renpy/common/00gltest.rpy:212
    old "This computer is using software rendering."
    new "This computer is using software rendering."

    # renpy/common/00gltest.rpy:214
    old "This game requires use of GL2 that can't be initialised."
    new "This game requires use of GL2 that can't be initialised."

    # renpy/common/00gltest.rpy:216
    old "This computer has a problem displaying graphics: [problem]."
    new "This computer has a problem displaying graphics: [problem]."

    # renpy/common/00gltest.rpy:220
    old "Its graphics drivers may be out of date or not operating correctly. This can lead to slow or incorrect graphics display."
    new "Its graphics drivers may be out of date or not operating correctly. This can lead to slow or incorrect graphics display."

    # renpy/common/00gltest.rpy:224
    old "The {a=edit:1:log.txt}log.txt{/a} file may contain information to help you determine what is wrong with your computer."
    new "The {a=edit:1:log.txt}log.txt{/a} file may contain information to help you determine what is wrong with your computer."

    # renpy/common/00gltest.rpy:229
    old "More details on how to fix this can be found in the {a=[url]}documentation{/a}."
    new "More details on how to fix this can be found in the {a=[url]}documentation{/a}."

    # renpy/common/00gltest.rpy:234
    old "Continue, Show this warning again"
    new "Continue, Show this warning again"

    # renpy/common/00gltest.rpy:238
    old "Continue, Don't show warning again"
    new "Continue, Don't show warning again"

    # renpy/common/00gltest.rpy:246
    old "Change render options"
    new "Change render options"

    # renpy/common/00gamepad.rpy:33
    old "Select Gamepad to Calibrate"
    new "Select Gamepad to Calibrate"

    # renpy/common/00gamepad.rpy:36
    old "No Gamepads Available"
    new "No Gamepads Available"

    # renpy/common/00gamepad.rpy:56
    old "Calibrating [name] ([i]/[total])"
    new "Calibrating [name] ([i]/[total])"

    # renpy/common/00gamepad.rpy:60
    old "Press or move the '[control!s]' [kind]."
    new "Press or move the '[control!s]' [kind]."

    # renpy/common/00gamepad.rpy:70
    old "Skip (A)"
    new "Skip (A)"

    # renpy/common/00gamepad.rpy:73
    old "Back (B)"
    new "Back (B)"

    # renpy/common/_errorhandling.rpym:757
    old "Open"
    new "Open"

    # renpy/common/_errorhandling.rpym:759
    old "Opens the traceback.txt file in a text editor."
    new "Opens the traceback.txt file in a text editor."

    # renpy/common/_errorhandling.rpym:761
    old "Copy BBCode"
    new "Copy BBCode"

    # renpy/common/_errorhandling.rpym:763
    old "Copies the traceback.txt file to the clipboard as BBcode for forums like https://lemmasoft.renai.us/."
    new "Copies the traceback.txt file to the clipboard as BBcode for forums like https://lemmasoft.renai.us/."

    # renpy/common/_errorhandling.rpym:765
    old "Copy Markdown"
    new "Copy Markdown"

    # renpy/common/_errorhandling.rpym:767
    old "Copies the traceback.txt file to the clipboard as Markdown for Discord."
    new "Copies the traceback.txt file to the clipboard as Markdown for Discord."

    # renpy/common/_errorhandling.rpym:799
    old "An exception has occurred."
    new "An exception has occurred."

    # renpy/common/_errorhandling.rpym:828
    old "Rollback"
    new "Rollback"

    # renpy/common/_errorhandling.rpym:830
    old "Attempts a roll back to a prior time, allowing you to save or choose a different choice."
    new "Attempts a roll back to a prior time, allowing you to save or choose a different choice."

    # renpy/common/_errorhandling.rpym:833
    old "Ignore"
    new "Ignore"

    # renpy/common/_errorhandling.rpym:837
    old "Ignores the exception, allowing you to continue."
    new "Ignores the exception, allowing you to continue."

    # renpy/common/_errorhandling.rpym:839
    old "Ignores the exception, allowing you to continue. This often leads to additional errors."
    new "Ignores the exception, allowing you to continue. This often leads to additional errors."

    # renpy/common/_errorhandling.rpym:843
    old "Reload"
    new "Reload"

    # renpy/common/_errorhandling.rpym:845
    old "Reloads the game from disk, saving and restoring game state if possible."
    new "Reloads the game from disk, saving and restoring game state if possible."

    # renpy/common/_errorhandling.rpym:848
    old "Console"
    new "Console"

    # renpy/common/_errorhandling.rpym:850
    old "Opens a console to allow debugging the problem."
    new "Opens a console to allow debugging the problem."

    # renpy/common/_errorhandling.rpym:863
    old "Quits the game."
    new "Quits the game."

    # renpy/common/_errorhandling.rpym:885
    old "Parsing the script failed."
    new "Parsing the script failed."

    # renpy/common/_developer/developer.rpym:39
    old "Developer Menu"
    new "Developer Menu"

    # renpy/common/_developer/developer.rpym:44
    old "Interactive Director (D)"
    new "Interactive Director (D)"

    # renpy/common/_developer/developer.rpym:46
    old "Reload Game (Shift+R)"
    new "Reload Game (Shift+R)"

    # renpy/common/_developer/developer.rpym:48
    old "Console (Shift+O)"
    new "Console (Shift+O)"

    # renpy/common/_developer/developer.rpym:50
    old "Variable Viewer"
    new "Variable Viewer"

    # renpy/common/_developer/developer.rpym:52
    old "Persistent Viewer"
    new "Persistent Viewer"

    # renpy/common/_developer/developer.rpym:54
    old "Image Location Picker"
    new "Image Location Picker"

    # renpy/common/_developer/developer.rpym:56
    old "Filename List"
    new "Filename List"

    # renpy/common/_developer/developer.rpym:60
    old "Show Image Load Log (F4)"
    new "Show Image Load Log (F4)"

    # renpy/common/_developer/developer.rpym:63
    old "Hide Image Load Log (F4)"
    new "Hide Image Load Log (F4)"

    # renpy/common/_developer/developer.rpym:66
    old "Image Attributes"
    new "Image Attributes"

    # renpy/common/_developer/developer.rpym:70
    old "Show Translation Info"
    new "Show Translation Info"

    # renpy/common/_developer/developer.rpym:73
    old "Hide Translation Info"
    new "Hide Translation Info"

    # renpy/common/_developer/developer.rpym:78
    old "Speech Bubble Editor (Shift+B)"
    new "Speech Bubble Editor (Shift+B)"

    # renpy/common/_developer/developer.rpym:82
    old "Show Filename and Line"
    new "Show Filename and Line"

    # renpy/common/_developer/developer.rpym:85
    old "Hide Filename and Line"
    new "Hide Filename and Line"

    # renpy/common/_developer/developer.rpym:141
    old "Layer [l]:"
    new "Layer [l]:"

    # renpy/common/_developer/developer.rpym:144
    old "    (transforms: [', '.join(transform_list)])"
    new "    (transforms: [', '.join(transform_list)])"

    # renpy/common/_developer/developer.rpym:148
    old "    [name!q] [attributes!q] (hidden)"
    new "    [name!q] [attributes!q] (hidden)"

    # renpy/common/_developer/developer.rpym:152
    old "    [name!q] [attributes!q]"
    new "    [name!q] [attributes!q]"

    # renpy/common/_developer/developer.rpym:205
    old "Nothing to inspect."
    new "Nothing to inspect."

    # renpy/common/_developer/developer.rpym:216
    old "Hide deleted"
    new "Hide deleted"

    # renpy/common/_developer/developer.rpym:216
    old "Show deleted"
    new "Show deleted"

    # renpy/common/_developer/developer.rpym:367
    old "Rectangle copied to clipboard."
    new "Rectangle copied to clipboard."

    # renpy/common/_developer/developer.rpym:370
    old "Position copied to clipboard."
    new "Position copied to clipboard."

    # renpy/common/_developer/developer.rpym:382
    old "Rectangle: %r"
    new "Rectangle: %r"

    # renpy/common/_developer/developer.rpym:385
    old "Mouse position: %r"
    new "Mouse position: %r"

    # renpy/common/_developer/developer.rpym:390
    old "Right-click or escape to quit."
    new "Right-click or escape to quit."

    # renpy/common/_developer/developer.rpym:440
    old "Type to filter: "
    new "Type to filter: "

    # renpy/common/_developer/developer.rpym:556
    old "Textures: [tex_count] ([tex_size_mb:.1f] MB)"
    new "Textures: [tex_count] ([tex_size_mb:.1f] MB)"

    # renpy/common/_developer/developer.rpym:560
    old "Image cache: [cache_pct:.1f]% ([cache_size_mb:.1f] MB)"
    new "Image cache: [cache_pct:.1f]% ([cache_size_mb:.1f] MB)"

    # renpy/common/_developer/developer.rpym:570
    old "✔ "
    new "✔ "

    # renpy/common/_developer/developer.rpym:573
    old "✘ "
    new "✘ "

    # renpy/common/_developer/developer.rpym:578
    old "\n{color=#cfc}✔ predicted image (good){/color}\n{color=#fcc}✘ unpredicted image (bad){/color}\n{color=#fff}Drag to move.{/color}"
    new "\n{color=#cfc}✔ predicted image (good){/color}\n{color=#fcc}✘ unpredicted image (bad){/color}\n{color=#fff}Drag to move.{/color}"

    # renpy/common/_developer/developer.rpym:628
    old "Click to open in editor."
    new "Click to open in editor."

    # renpy/common/_developer/inspector.rpym:39
    old "Displayable Inspector"
    new "Displayable Inspector"

    # renpy/common/_developer/inspector.rpym:62
    old "Size"
    new "Size"

    # renpy/common/_developer/inspector.rpym:66
    old "Style"
    new "Style"

    # renpy/common/_developer/inspector.rpym:72
    old "Location"
    new "Location"

    # renpy/common/_developer/inspector.rpym:124
    old "Inspecting Styles of [displayable_name!q]"
    new "Inspecting Styles of [displayable_name!q]"

    # renpy/common/_developer/inspector.rpym:141
    old "displayable:"
    new "displayable:"

    # renpy/common/_developer/inspector.rpym:147
    old "        (no properties affect the displayable)"
    new "        (no properties affect the displayable)"

    # renpy/common/_developer/inspector.rpym:149
    old "        (default properties omitted)"
    new "        (default properties omitted)"

    # renpy/common/_developer/inspector.rpym:187
    old "<repr() failed>"
    new "<repr() failed>"

    # renpy/common/00console.rpy:552
    old "Press <esc> to exit console. Type help for help.\n"
    new "Press <esc> to exit console. Type help for help.\n"

    # renpy/common/00console.rpy:556
    old "Ren'Py script enabled."
    new "Ren'Py script enabled."

    # renpy/common/00console.rpy:558
    old "Ren'Py script disabled."
    new "Ren'Py script disabled."

    # renpy/common/00console.rpy:741
    old "The console is using short representations. To disable this, type 'long', and to re-enable, type 'short'"
    new "The console is using short representations. To disable this, type 'long', and to re-enable, type 'short'"

    # renpy/common/00console.rpy:810
    old "help: show this help\n help <expr>: show signature and documentation of <expr>"
    new "help: show this help\n help <expr>: show signature and documentation of <expr>"

    # renpy/common/00console.rpy:834
    old "Help may display undocumented functions. Please check that the function or\nclass you want to use is documented.\n\n"
    new "Help may display undocumented functions. Please check that the function or\nclass you want to use is documented.\n\n"

    # renpy/common/00console.rpy:843
    old "commands:\n"
    new "commands:\n"

    # renpy/common/00console.rpy:853
    old " <renpy script statement>: run the statement\n"
    new " <renpy script statement>: run the statement\n"

    # renpy/common/00console.rpy:855
    old " <python expression or statement>: run the expression or statement"
    new " <python expression or statement>: run the expression or statement"

    # renpy/common/00console.rpy:863
    old "clear: clear the console history"
    new "clear: clear the console history"

    # renpy/common/00console.rpy:867
    old "exit: exit the console"
    new "exit: exit the console"

    # renpy/common/00console.rpy:875
    old "stack: print the return stack"
    new "stack: print the return stack"

    # renpy/common/00console.rpy:897
    old "load <slot>: loads the game from slot"
    new "load <slot>: loads the game from slot"

    # renpy/common/00console.rpy:910
    old "save <slot>: saves the game in slot"
    new "save <slot>: saves the game in slot"

    # renpy/common/00console.rpy:921
    old "reload: reloads the game, refreshing the scripts"
    new "reload: reloads the game, refreshing the scripts"

    # renpy/common/00console.rpy:929
    old "watch <expression>: watch a python expression\n watch short: makes the representation of traced expressions short (default)\n watch long: makes the representation of traced expressions as is"
    new "watch <expression>: watch a python expression\n watch short: makes the representation of traced expressions short (default)\n watch long: makes the representation of traced expressions as is"

    # renpy/common/00console.rpy:966
    old "unwatch <expression>: stop watching an expression"
    new "unwatch <expression>: stop watching an expression"

    # renpy/common/00console.rpy:1012
    old "unwatchall: stop watching all expressions"
    new "unwatchall: stop watching all expressions"

    # renpy/common/00console.rpy:1033
    old "jump <label>: jumps to label"
    new "jump <label>: jumps to label"

    # renpy/common/00console.rpy:1049
    old "short: Shorten the representation of objects on the console (default)."
    new "short: Shorten the representation of objects on the console (default)."

    # renpy/common/00console.rpy:1053
    old "long: Print the full representation of objects on the console."
    new "long: Print the full representation of objects on the console."

    # renpy/common/00console.rpy:1057
    old "escape: Enables escaping of unicode symbols in unicode strings."
    new "escape: Enables escaping of unicode symbols in unicode strings."

    # renpy/common/00console.rpy:1061
    old "unescape: Disables escaping of unicode symbols in unicode strings and print it as is (default)."
    new "unescape: Disables escaping of unicode symbols in unicode strings and print it as is (default)."

