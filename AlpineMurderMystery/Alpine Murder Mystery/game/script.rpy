label dev_quickjump:
    menu:
        "Developer Quick Jump Menu"
        "Scene 1 – Train Station Arrival":
            jump scene1_start
        "Scene 2 – Cable Car":
            jump scene2_start
        "Scene 3 – Hotel Arrival":
            jump scene3_start
        "Scene 4 – Private Moments":
            jump scene4_start
        "Scene 5 – Lounge / Dinner":
            jump scene5_start
        "Play full game from start":
            jump scene1_start

label start:
    if config.developer:
        jump dev_quickjump
    else:
        jump scene1_start

####################################
# Scene 1: Train Station Arrival
####################################
label scene1_start:
    play ambience "audio/ambience/amb_station_wind.ogg" fadein 1.0
    play music "audio/music/music_arrival_theme.ogg" fadein 2.0

    scene bg station_wide with fade
    n "The last train of the day pulls into the remote alpine station."
    n "The Grand Glacier Hotel sits high above, shrouded in snow."

    scene bg station_train_arrival with fade
    play sound "audio/sfx/metro-train-door-close-01.ogg"
    n "Passengers step off into the cold, crisp air."

    # Markus arrival
    scene bg station_markus with fade
    n "A distinguished man steps off — <PERSON>."

    # Sophia arrival
    scene bg station_sophia with fade
    n "<PERSON>, her eyes sharp, scans the platform."

    # Dr. <PERSON>
    scene bg station_wilson with fade
    n "Dr. <PERSON> <PERSON>, confident and composed."

    # Countess
    scene bg station_countess with fade
    n "Countess Von Schreiber, every movement refined."

    # <PERSON> & Marie
    scene bg station_thomas with fade
    n "The French newlyweds, <PERSON>..."

    scene bg station_marie with fade
    n "...and Marie Durant."

####################################
# Scene 2: Cable Car Station
####################################
label scene2_start:
    stop music fadeout 2.0
    play music "audio/music/music_ascent.ogg" fadein 2.0

    scene bg cable_station with fade
    n "The only way up is the old cable car."

    scene bg cable_interior with fade
    play ambience "audio/ambience/cable-cars-on-tracks-56549.ogg" fadein 1.0
    n "Inside, the strangers ride in silence, their luggage at their feet."

    scene bg cable_exterior with fade
    n "The mountain looms above them, the hotel barely visible in the mist."

####################################
# Scene 3: Hotel Arrival
####################################
label scene3_start:
    stop ambience fadeout 1.5
    play ambience "audio/ambience/fireplace-01.ogg" fadein 1.0
    stop music fadeout 2.0
    play music "audio/music/mysterious-piano-and-violin-atmosphere-short-version-199232.ogg" fadein 2.0

    scene bg hotel_platform with fade
    n "The cable car docks at the snowy platform."

    scene bg hotel_entrance with fade
    n "The guests approach the grand entrance."

    scene bg lobby_wide with fade
    n "Warmth greets them inside the Grand Glacier Hotel."

    scene bg checkin_markus with fade
    play sound "audio/sfx/keys-jingle-03.ogg"
    n "Elena Rossi, the hotel manager, greets Markus Blackwood."

    scene bg checkin_french_couple with fade
    n "She welcomes the French couple with a warm smile."

####################################
# Scene 4: Private Moments
####################################
label scene4_start:
    stop ambience fadeout 1.0
    play ambience "audio/ambience/footsteps-wooden-floor-01.ogg" fadein 1.0
    stop music fadeout 2.0
    play music "audio/music/lofi-ambient-pianoline-116134.ogg" fadein 2.0

    scene bg corridor with fade
    n "Guests retire to their rooms, the corridor stretching in silence."

    scene bg wilson_room with fade
    play ambience "audio/sfx/light-switch-01.ogg" fadein 1.0
    play ambience "audio/sfx/bag-zipper-02.ogg" fadein 1.0
    n "In his room, James Wilson unpacks with meticulous care."

####################################
# Scene 5: Lounge/Dinner
####################################
label scene5_start:
    stop ambience fadeout 1.5
    play ambience "audio/ambience/crowd-cafe.ogg" fadein 1.0
    stop music fadeout 2.0
    play music "audio/music/mysterious-piano-and-violin-atmosphere-short-version-199232.ogg" fadein 2.0

    scene bg lounge_french_couple with fade
    play sound "audio/sfx/coffee-cup-set-down-02.ogg"
    play sound "audio/ambience/champagne-glass-clink-01.ogg"
    n "Marie and Thomas sit in the corner, speaking quietly."

    scene bg lounge_countess with fade
    n "The Countess warms herself by the fire."

    scene bg lounge_sophia with fade
    n "Sophia stands by the window, watching the storm."

    scene bg lounge_wilson_elena with fade
    n "At the bar, Dr. Wilson speaks with Elena Rossi."

    scene bg lounge_markus with fade
    n "In another corner, Markus Blackwood sits alone, writing diligently on a piece of paper."

    scene bg lounge_storm with fade
    play ambience "audio/ambience/amb_wind_strong.ogg" fadein 1.0
    n "Outside, the storm intensifies, pressing against the glass."

    ####################################
    # Player Choice
    ####################################
    n "Where do you go?"
    menu:
        "Sit by the fire":
            jump choice_fire
        "Approach the bar":
            jump choice_bar
        "Join the corner table":
            jump choice_couple

label choice_fire:
    n "You sit by the fire, overhearing the Countess speaking with Sophia."
    jump after_choice

label choice_bar:
    n "You approach the bar, catching fragments of Dr. Wilson and Elena's conversation."
    jump after_choice

label choice_couple:
    n "You join the French couple, who greet you politely."
    jump after_choice

label after_choice:
    stop ambience fadeout 1.5
    stop music fadeout 2.0
    play ambience "audio/ambience/amb_wind_strong.ogg" fadein 1.0
    play music "audio/music/background-tension-building.ogg" fadein 2.0

    scene bg lounge_storm with fade
    play sound "audio/sfx/light-flicker.ogg"
    n "The lights flicker once. A hush falls over the room."
    n "The mountain is closing in... and soon, so will the night."

    return
