# macOS
.DS_Store
**/.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~

# Ren'Py specific files
*.rpyc
*.rpymc
*.rpyb
cache/
saves/
persistent
log.txt
errors.txt
traceback.txt
*.save

# Ren'Py build outputs
*.app/
*.exe
*.zip
*.tar.bz2
*.dmg
*.pkg
*-dists/
tmp/

# Ren'Py Android build
*.apk
*.aab
rapt/
android.keystore

# Ren'Py iOS build
ios-*/

# Ren'Py Web build
web-*/

# Development and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.pydevproject

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Backup files
*.bak
*.backup
*.old
*.orig

# Temporary files
*.tmp
*.temp

# Game-specific (optional - uncomment if needed)
# screenshots/
# movies/

# Image files in specific directories (including subdirectories)
characters/**/*.jpg
characters/**/*.jpeg
characters/**/*.png
characters/**/*.gif
characters/**/*.bmp
characters/**/*.tiff
characters/**/*.webp
scenes/**/*.jpg
scenes/**/*.jpeg
scenes/**/*.png
scenes/**/*.gif
scenes/**/*.bmp
scenes/**/*.tiff
scenes/**/*.webp

# Video files in specific directories (including subdirectories)
characters/**/*.mp4
characters/**/*.avi
characters/**/*.mov
characters/**/*.wmv
characters/**/*.flv
characters/**/*.webm
characters/**/*.mkv
characters/**/*.m4v
characters/**/*.3gp
characters/**/*.ogv
scenes/**/*.mp4
scenes/**/*.avi
scenes/**/*.mov
scenes/**/*.wmv
scenes/**/*.flv
scenes/**/*.webm
scenes/**/*.mkv
scenes/**/*.m4v
scenes/**/*.3gp
scenes/**/*.ogv

audio/
renpy/AlpineMurderMystery/Alpine Murder Mystery/game/images/*
renpy/AlpineMurderMystery/Alpine Murder Mystery/game/audio/*
renpy/AlpineMurderMystery/Alpine Murder Mystery/game/libs/*

AlpineMurderMystery/Alpine Murder Mystery/game/images/*
AlpineMurderMystery/Alpine Murder Mystery/game/libs/*
AlpineMurderMystery/Alpine Murder Mystery/game/audio/*